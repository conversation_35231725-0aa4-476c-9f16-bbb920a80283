[2025-07-02 04:27:17] local.ERROR: SQLSTATE[HY000]: General error: 1 table positions has no column named min_salary (Connection: sqlite, SQL: insert into "positions" ("title", "description", "department_id", "level", "min_salary", "max_salary", "requirements", "responsibilities", "is_active", "updated_at", "created_at") values (Emergency Medicine Physician, Provides emergency medical care and treatment, 1, Senior, 25000000, 40000000, ["Medical degree","Emergency medicine specialization","Valid medical license"], ["Emergency patient care","Trauma management","Critical decision making"], 1, 2025-07-02 04:27:17, 2025-07-02 04:27:17)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 table positions has no column named min_salary (Connection: sqlite, SQL: insert into \"positions\" (\"title\", \"description\", \"department_id\", \"level\", \"min_salary\", \"max_salary\", \"requirements\", \"responsibilities\", \"is_active\", \"updated_at\", \"created_at\") values (Emergency Medicine Physician, Provides emergency medical care and treatment, 1, Senior, 25000000, 40000000, [\"Medical degree\",\"Emergency medicine specialization\",\"Valid medical license\"], [\"Emergency patient care\",\"Trauma management\",\"Critical decision making\"], 1, 2025-07-02 04:27:17, 2025-07-02 04:27:17)) at D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"po...', Array, Object(Closure))
#1 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"po...', Array, Object(Closure))
#2 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"po...', Array)
#3 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"po...', Array)
#4 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"po...', Array, 'id')
#5 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#6 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1410): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#7 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1375): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#8 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1214): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#10 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\Position))
#11 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap(Object(App\\Models\\Position), Object(Closure))
#12 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#13 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2496): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#14 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2512): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#15 D:\\majemen_karyawan\\hospital-employee-management\\database\\seeders\\PositionSeeder.php(253): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#16 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\PositionSeeder->run()
#17 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#22 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#23 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(61): Illuminate\\Database\\Seeder->__invoke(Array)
#24 D:\\majemen_karyawan\\hospital-employee-management\\database\\seeders\\DatabaseSeeder.php(15): Illuminate\\Database\\Seeder->call('Database\\\\Seeder...')
#25 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#26 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#27 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#28 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#29 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#30 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#31 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#32 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#33 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#34 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#35 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#36 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#37 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#38 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#39 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#40 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#41 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#42 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#43 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#44 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand('db:seed', Array, Object(Illuminate\\Console\\OutputStyle))
#45 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(121): Illuminate\\Console\\Command->call('db:seed', Array)
#46 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(97): Illuminate\\Database\\Console\\Migrations\\FreshCommand->runSeeder(NULL)
#47 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#48 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#49 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#50 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#51 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#52 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#53 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#54 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#55 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#56 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#57 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#58 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#59 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#60 D:\\majemen_karyawan\\hospital-employee-management\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#61 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 table positions has no column named min_salary at D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare('insert into \"po...')
#1 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into \"po...', Array)
#2 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"po...', Array, Object(Closure))
#3 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"po...', Array, Object(Closure))
#4 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"po...', Array)
#5 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"po...', Array)
#6 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"po...', Array, 'id')
#7 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#8 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1410): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#9 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1375): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#10 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1214): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#11 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#12 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\Position))
#13 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap(Object(App\\Models\\Position), Object(Closure))
#14 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#15 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2496): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#16 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2512): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#17 D:\\majemen_karyawan\\hospital-employee-management\\database\\seeders\\PositionSeeder.php(253): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#18 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\PositionSeeder->run()
#19 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#24 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#25 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(61): Illuminate\\Database\\Seeder->__invoke(Array)
#26 D:\\majemen_karyawan\\hospital-employee-management\\database\\seeders\\DatabaseSeeder.php(15): Illuminate\\Database\\Seeder->call('Database\\\\Seeder...')
#27 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#28 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#29 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#30 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#31 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#32 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#33 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#34 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#35 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#36 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#37 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#38 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#39 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#40 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#41 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#42 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#43 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#44 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#45 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#46 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand('db:seed', Array, Object(Illuminate\\Console\\OutputStyle))
#47 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(121): Illuminate\\Console\\Command->call('db:seed', Array)
#48 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(97): Illuminate\\Database\\Console\\Migrations\\FreshCommand->runSeeder(NULL)
#49 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#50 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#51 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#52 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#53 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#54 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#55 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#56 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#57 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#58 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#59 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#60 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#61 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#62 D:\\majemen_karyawan\\hospital-employee-management\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#63 {main}
"} 
[2025-07-02 04:27:58] local.ERROR: SQLSTATE[HY000]: General error: 1 table leave_types has no column named color (Connection: sqlite, SQL: insert into "leave_types" ("name", "description", "max_days_per_year", "requires_approval", "requires_document", "is_paid", "is_active", "color", "updated_at", "created_at") values (Annual Leave, Yearly vacation leave for rest and recreation, 12, 1, 0, 1, 1, #28a745, 2025-07-02 04:27:58, 2025-07-02 04:27:58)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 table leave_types has no column named color (Connection: sqlite, SQL: insert into \"leave_types\" (\"name\", \"description\", \"max_days_per_year\", \"requires_approval\", \"requires_document\", \"is_paid\", \"is_active\", \"color\", \"updated_at\", \"created_at\") values (Annual Leave, Yearly vacation leave for rest and recreation, 12, 1, 0, 1, 1, #28a745, 2025-07-02 04:27:58, 2025-07-02 04:27:58)) at D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"le...', Array, Object(Closure))
#1 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"le...', Array, Object(Closure))
#2 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"le...', Array)
#3 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"le...', Array)
#4 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"le...', Array, 'id')
#5 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#6 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1410): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#7 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1375): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#8 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1214): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#10 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\LeaveType))
#11 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap(Object(App\\Models\\LeaveType), Object(Closure))
#12 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#13 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2496): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#14 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2512): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#15 D:\\majemen_karyawan\\hospital-employee-management\\database\\seeders\\LeaveTypeSeeder.php(139): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#16 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\LeaveTypeSeeder->run()
#17 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#22 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#23 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(61): Illuminate\\Database\\Seeder->__invoke(Array)
#24 D:\\majemen_karyawan\\hospital-employee-management\\database\\seeders\\DatabaseSeeder.php(15): Illuminate\\Database\\Seeder->call('Database\\\\Seeder...')
#25 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#26 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#27 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#28 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#29 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#30 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#31 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#32 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#33 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#34 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#35 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#36 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#37 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#38 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#39 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#40 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#41 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#42 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#43 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#44 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand('db:seed', Array, Object(Illuminate\\Console\\OutputStyle))
#45 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(121): Illuminate\\Console\\Command->call('db:seed', Array)
#46 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(97): Illuminate\\Database\\Console\\Migrations\\FreshCommand->runSeeder(NULL)
#47 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#48 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#49 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#50 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#51 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#52 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#53 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#54 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#55 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#56 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#57 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#58 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#59 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#60 D:\\majemen_karyawan\\hospital-employee-management\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#61 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 table leave_types has no column named color at D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare('insert into \"le...')
#1 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into \"le...', Array)
#2 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"le...', Array, Object(Closure))
#3 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"le...', Array, Object(Closure))
#4 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"le...', Array)
#5 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"le...', Array)
#6 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"le...', Array, 'id')
#7 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#8 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1410): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#9 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1375): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#10 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1214): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#11 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#12 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\LeaveType))
#13 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap(Object(App\\Models\\LeaveType), Object(Closure))
#14 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#15 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2496): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#16 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2512): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#17 D:\\majemen_karyawan\\hospital-employee-management\\database\\seeders\\LeaveTypeSeeder.php(139): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#18 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\LeaveTypeSeeder->run()
#19 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#24 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#25 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(61): Illuminate\\Database\\Seeder->__invoke(Array)
#26 D:\\majemen_karyawan\\hospital-employee-management\\database\\seeders\\DatabaseSeeder.php(15): Illuminate\\Database\\Seeder->call('Database\\\\Seeder...')
#27 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#28 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#29 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#30 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#31 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#32 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#33 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#34 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#35 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#36 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#37 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#38 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#39 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#40 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#41 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#42 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#43 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#44 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#45 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#46 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand('db:seed', Array, Object(Illuminate\\Console\\OutputStyle))
#47 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(121): Illuminate\\Console\\Command->call('db:seed', Array)
#48 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(97): Illuminate\\Database\\Console\\Migrations\\FreshCommand->runSeeder(NULL)
#49 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#50 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#51 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#52 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#53 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#54 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#55 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#56 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#57 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#58 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#59 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#60 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#61 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#62 D:\\majemen_karyawan\\hospital-employee-management\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#63 {main}
"} 
[2025-07-02 04:28:06] local.ERROR: A `view employees` permission already exists for guard `web`. {"exception":"[object] (Spatie\\Permission\\Exceptions\\PermissionAlreadyExists(code: 0): A `view employees` permission already exists for guard `web`. at D:\\majemen_karyawan\\hospital-employee-management\\vendor\\spatie\\laravel-permission\\src\\Exceptions\\PermissionAlreadyExists.php:11)
[stacktrace]
#0 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\spatie\\laravel-permission\\src\\Models\\Permission.php(49): Spatie\\Permission\\Exceptions\\PermissionAlreadyExists::create('view employees', 'web')
#1 D:\\majemen_karyawan\\hospital-employee-management\\database\\seeders\\RolePermissionSeeder.php(112): Spatie\\Permission\\Models\\Permission::create(Array)
#2 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\RolePermissionSeeder->run()
#3 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#4 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#5 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#6 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#7 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#8 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#9 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(61): Illuminate\\Database\\Seeder->__invoke(Array)
#10 D:\\majemen_karyawan\\hospital-employee-management\\database\\seeders\\DatabaseSeeder.php(15): Illuminate\\Database\\Seeder->call('Database\\\\Seeder...')
#11 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#12 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#15 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#16 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#17 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#18 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#19 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#20 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#21 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#22 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#24 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#25 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#26 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#27 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\majemen_karyawan\\hospital-employee-management\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#35 {main}
"} 
[2025-07-02 04:28:44] local.ERROR: A `view employees` permission already exists for guard `web`. {"exception":"[object] (Spatie\\Permission\\Exceptions\\PermissionAlreadyExists(code: 0): A `view employees` permission already exists for guard `web`. at D:\\majemen_karyawan\\hospital-employee-management\\vendor\\spatie\\laravel-permission\\src\\Exceptions\\PermissionAlreadyExists.php:11)
[stacktrace]
#0 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\spatie\\laravel-permission\\src\\Models\\Permission.php(49): Spatie\\Permission\\Exceptions\\PermissionAlreadyExists::create('view employees', 'web')
#1 D:\\majemen_karyawan\\hospital-employee-management\\database\\seeders\\RolePermissionSeeder.php(112): Spatie\\Permission\\Models\\Permission::create(Array)
#2 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\RolePermissionSeeder->run()
#3 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#4 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#5 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#6 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#7 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#8 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#9 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#10 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#11 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#12 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#13 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#16 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#17 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#18 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#19 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#20 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#23 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 D:\\majemen_karyawan\\hospital-employee-management\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#26 {main}
"} 
[2025-07-03 03:27:45] local.ERROR: A `view employees` permission already exists for guard `web`. {"exception":"[object] (Spatie\\Permission\\Exceptions\\PermissionAlreadyExists(code: 0): A `view employees` permission already exists for guard `web`. at D:\\majemen_karyawan\\hospital-employee-management\\vendor\\spatie\\laravel-permission\\src\\Exceptions\\PermissionAlreadyExists.php:11)
[stacktrace]
#0 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\spatie\\laravel-permission\\src\\Models\\Permission.php(49): Spatie\\Permission\\Exceptions\\PermissionAlreadyExists::create('view employees', 'web')
#1 D:\\majemen_karyawan\\hospital-employee-management\\database\\seeders\\RolePermissionSeeder.php(112): Spatie\\Permission\\Models\\Permission::create(Array)
#2 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\RolePermissionSeeder->run()
#3 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#4 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#5 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#6 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#7 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#8 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#9 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(61): Illuminate\\Database\\Seeder->__invoke(Array)
#10 D:\\majemen_karyawan\\hospital-employee-management\\database\\seeders\\DatabaseSeeder.php(15): Illuminate\\Database\\Seeder->call('Database\\\\Seeder...')
#11 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#12 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#13 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#14 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#15 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#16 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#17 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#18 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#19 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#20 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#21 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#22 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#23 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#24 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#25 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#26 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#27 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\majemen_karyawan\\hospital-employee-management\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#35 {main}
"} 
[2025-07-03 03:28:32] local.ERROR: SQLSTATE[HY000]: General error: 1 table users has no column named is_active (Connection: sqlite, SQL: insert into "users" ("name", "email", "password", "email_verified_at", "is_active", "updated_at", "created_at") values (Super Administrator, <EMAIL>, $2y$12$cUwNU0LCjewVyR/LKRLOjef3H4hpp9Grrhu4ImUZE614wzVDD7hU6, 2025-07-03 03:28:32, 1, 2025-07-03 03:28:32, 2025-07-03 03:28:32)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 table users has no column named is_active (Connection: sqlite, SQL: insert into \"users\" (\"name\", \"email\", \"password\", \"email_verified_at\", \"is_active\", \"updated_at\", \"created_at\") values (Super Administrator, <EMAIL>, $2y$12$cUwNU0LCjewVyR/LKRLOjef3H4hpp9Grrhu4ImUZE614wzVDD7hU6, 2025-07-03 03:28:32, 1, 2025-07-03 03:28:32, 2025-07-03 03:28:32)) at D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"us...', Array, Object(Closure))
#1 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"us...', Array, Object(Closure))
#2 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"us...', Array)
#3 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"us...', Array)
#4 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"us...', Array, 'id')
#5 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#6 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1410): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#7 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1375): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#8 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1214): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#10 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\User))
#11 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap(Object(App\\Models\\User), Object(Closure))
#12 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#13 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2496): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#14 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2512): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#15 D:\\majemen_karyawan\\hospital-employee-management\\database\\seeders\\UserSeeder.php(25): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#16 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\UserSeeder->run()
#17 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#22 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#23 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#24 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#25 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#26 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#27 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#29 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#30 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#31 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#32 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#33 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#34 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 D:\\majemen_karyawan\\hospital-employee-management\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#40 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 table users has no column named is_active at D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare('insert into \"us...')
#1 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into \"us...', Array)
#2 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into \"us...', Array, Object(Closure))
#3 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('insert into \"us...', Array, Object(Closure))
#4 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement('insert into \"us...', Array)
#5 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"us...', Array)
#6 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"us...', Array, 'id')
#7 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#8 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1410): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#9 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1375): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#10 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1214): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#11 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#12 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\User))
#13 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap(Object(App\\Models\\User), Object(Closure))
#14 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#15 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2496): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#16 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2512): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#17 D:\\majemen_karyawan\\hospital-employee-management\\database\\seeders\\UserSeeder.php(25): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#18 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\UserSeeder->run()
#19 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#24 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#25 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#26 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#27 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#28 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#29 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#30 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#31 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#32 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#33 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#34 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#35 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#36 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 D:\\majemen_karyawan\\hospital-employee-management\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 D:\\majemen_karyawan\\hospital-employee-management\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#42 {main}
"} 
