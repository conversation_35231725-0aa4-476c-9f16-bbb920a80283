<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payrolls', function (Blueprint $table) {
            $table->id();
            $table->foreignId('employee_id')->constrained()->onDelete('cascade');
            $table->string('payroll_period'); // e.g., "2024-01" for January 2024
            $table->date('pay_date');

            // Earnings
            $table->decimal('base_salary', 10, 2);
            $table->decimal('overtime_pay', 10, 2)->default(0);
            $table->decimal('allowances', 10, 2)->default(0); // Medical, transport, etc.
            $table->decimal('bonuses', 10, 2)->default(0);
            $table->decimal('gross_salary', 10, 2);

            // Deductions
            $table->decimal('tax_deduction', 10, 2)->default(0);
            $table->decimal('insurance_deduction', 10, 2)->default(0);
            $table->decimal('pension_deduction', 10, 2)->default(0);
            $table->decimal('other_deductions', 10, 2)->default(0);
            $table->decimal('total_deductions', 10, 2)->default(0);

            // Final amounts
            $table->decimal('net_salary', 10, 2);

            // Additional information
            $table->integer('working_days');
            $table->integer('present_days');
            $table->integer('overtime_hours')->default(0);
            $table->json('allowance_details')->nullable(); // Breakdown of allowances
            $table->json('deduction_details')->nullable(); // Breakdown of deductions
            $table->text('notes')->nullable();
            $table->enum('status', ['draft', 'approved', 'paid'])->default('draft');
            $table->foreignId('processed_by')->constrained('users');
            $table->timestamps();

            $table->index(['employee_id', 'payroll_period']);
            $table->index(['payroll_period']);
            $table->index(['status']);
            $table->unique(['employee_id', 'payroll_period']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payrolls');
    }
};
